@import 'tailwindcss';

/* Space Theme Animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

/* Company Logos Slideshow Animation */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 20s linear infinite;
}

/* Animated Stars Background */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="0.5" fill="white" opacity="0.8"/><circle cx="80" cy="40" r="0.3" fill="white" opacity="0.6"/><circle cx="40" cy="60" r="0.4" fill="white" opacity="0.7"/><circle cx="90" cy="80" r="0.2" fill="white" opacity="0.5"/><circle cx="10" cy="90" r="0.3" fill="white" opacity="0.6"/><circle cx="70" cy="10" r="0.4" fill="white" opacity="0.8"/><circle cx="30" cy="30" r="0.2" fill="white" opacity="0.4"/><circle cx="60" cy="70" r="0.3" fill="white" opacity="0.7"/><circle cx="85" cy="15" r="0.2" fill="white" opacity="0.5"/><circle cx="15" cy="50" r="0.4" fill="white" opacity="0.6"/></svg>')
    repeat;
  background-size: 200px 200px;
  animation: move-stars 20s linear infinite;
}

.twinkling {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="0.3" fill="white" opacity="0.9"/><circle cx="75" cy="75" r="0.2" fill="white" opacity="0.8"/><circle cx="50" cy="10" r="0.4" fill="white" opacity="0.7"/><circle cx="10" cy="60" r="0.2" fill="white" opacity="0.6"/><circle cx="90" cy="30" r="0.3" fill="white" opacity="0.8"/></svg>')
    repeat;
  background-size: 300px 300px;
  animation: move-twinkling 30s linear infinite;
}

@keyframes move-stars {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-200px);
  }
}

@keyframes move-twinkling {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-300px);
  }
}

/* Glowing effects for space theme */
.glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
}

.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* Custom scrollbar for space theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #8b5cf6, #3b82f6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #7c3aed, #2563eb);
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  /* Performance optimizations */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Security: Prevent text selection on sensitive elements */
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  /* Accessibility */
  scroll-behavior: smooth;
}

/* Performance: GPU acceleration for animations */
.animate-pulse,
.animate-bounce,
.animate-ping,
.animate-spin {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimized transitions */
.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.fast-transition {
  transition: all 0.15s ease-out;
}

/* Reduce backdrop blur for better performance */
.backdrop-blur-light {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.backdrop-blur-medium {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Optimized hover effects */
.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Optimize carousel transitions */
.carousel-container {
  will-change: transform;
  transform: translateZ(0);
}

.carousel-slide {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Security: Hide content from screen readers when needed */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Performance: Optimize images */
img {
  max-width: 100%;
  height: auto;
  /* Lazy loading hint */
  loading: lazy;
  /* Prevent layout shift */
  aspect-ratio: attr(width) / attr(height);
}

/* Security: Prevent clickjacking */
iframe {
  border: 0;
  sandbox: allow-scripts allow-same-origin;
}

/* Performance: Optimize focus states */
:focus-visible {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}

/* Remove default focus for mouse users */
:focus:not(:focus-visible) {
  outline: none;
}

/* Performance: Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-pulse,
  .animate-bounce,
  .animate-ping,
  .animate-spin {
    animation: none !important;
  }
}
